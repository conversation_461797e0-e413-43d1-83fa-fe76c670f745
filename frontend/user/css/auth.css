/* 认证页面样式 */

/* 通用样式 */
.hidden {
    display: none !important;
}

.auth-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
}

.auth-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
}

.back-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 5px;
}

.back-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.auth-title {
    font-size: 1.5rem;
    font-weight: 600;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    display: flex;
    align-items: center;
    gap: 10px;
}

.auth-form-container {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    width: 100%;
}

.auth-form-wrapper {
    width: 100%;
    max-width: 400px;
    margin: -120px auto 0;
    padding: 0px;
}

.auth-logo {
    text-align: center;
    margin-bottom: 30px;
    color: #fff;
}

.auth-logo i {
    font-size: 4rem;
    color: #00ff88;
    margin-bottom: 15px;
    display: block;
    text-shadow: 0 0 20px rgba(0, 255, 136, 0.5);
}

.auth-logo h2 {
    font-size: 2.5rem;
    font-weight: 800;
    margin: 0 0 10px 0;
    color: #fff;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.auth-logo p {
    font-size: 1.1rem;
    color: #fff;
    margin: 0;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.auth-form {
    background: white;
    border-radius: 20px;
    padding: 40px 0;
    box-shadow: 0 20px 60px rgba(0,0,0,0.2);
    width: 100%;
    max-width: 400px;
    backdrop-filter: blur(10px);
}

.auth-form > * {
    padding-left: 40px;
    padding-right: 40px;
}

.form-title {
    text-align: center;
    font-size: 2rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
}

.form-subtitle {
    text-align: center;
    color: #666;
    margin-bottom: 30px;
    font-size: 0.9rem;
}

.login-form {
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 25px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.form-group label i {
    color: #667eea;
    width: 16px;
}

.form-group input {
    width: 100%;
    padding: 15px;
    border: 2px solid #e1e5e9;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.form-group input:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group input::placeholder {
    color: #adb5bd;
}

.password-input {
    position: relative;
}

.password-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.password-input-wrapper input {
    padding-right: 45px !important;
}

.password-toggle {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: color 0.3s ease;
}

.password-toggle:hover {
    color: #667eea;
}

.form-actions {
    margin-bottom: 20px;
}

.auth-btn {
    width: 60%;
    max-width: 200px;
    margin: 0 auto;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 15px;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    position: relative;
}

.auth-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
}

.auth-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.btn-loading {
    position: absolute;
    right: 15px;
}

.auth-links {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

.auth-footer {
    text-align: center;
    margin-top: 20px;
}

.auth-tips {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.auth-tips i {
    color: rgba(255, 255, 255, 0.6);
}

.link-home {
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 5px;
    transition: color 0.3s ease;
}

.link-home:hover {
    color: #5a67d8;
    text-decoration: underline;
}

.error-message {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: #f8d7da;
    color: #721c24;
    padding: 15px 20px;
    border-radius: 10px;
    border: 1px solid #f5c6cb;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 10px;
    z-index: 1000;
    max-width: 90%;
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

/* 移动端适配 */
@media (max-width: 768px) {
    .auth-header {
        padding: 12px 15px;
    }

    .auth-title {
        font-size: 1.2rem;
    }

    .back-btn {
        padding: 8px 12px;
        font-size: 0.8rem;
    }

    .back-btn i {
        font-size: 0.9rem;
    }

    .auth-form-container {
        padding: 15px;
    }

    .auth-form {
        padding: 30px 0;
        border-radius: 15px;
        box-shadow: 0 15px 40px rgba(0,0,0,0.25);
    }

    .auth-form > * {
        padding-left: 25px;
        padding-right: 25px;
    }

    .form-title {
        font-size: 1.5rem;
    }

    .form-group input {
        padding: 14px;
        font-size: 16px; /* 防止iOS自动缩放 */
        border-radius: 10px;
    }

    .auth-btn {
        padding: 15px;
        font-size: 1rem;
        border-radius: 10px;
    }

    .error-message {
        font-size: 0.9rem;
        padding: 12px 16px;
    }

    .auth-logo {
        margin-bottom: 25px;
    }

    .auth-logo i {
        font-size: 3.5rem;
        margin-bottom: 12px;
    }

    .auth-logo h2 {
        font-size: 2rem;
        font-weight: 800;
    }

    .auth-logo p {
        font-size: 1rem;
        font-weight: 600;
    }

    .auth-tips {
        font-size: 0.85rem;
    }

    .auth-form-wrapper {
        margin: -120px auto 0;
        padding: 0px;
    }
}

@media (max-width: 480px) {
    .auth-header {
        padding: 10px 12px;
    }

    .back-btn {
        padding: 6px 10px;
        font-size: 0.7rem;
    }

    .auth-title {
        font-size: 1rem;
    }
    
    .auth-form {
        padding: 25px 0;
        margin: 0;
    }

    .auth-form > * {
        padding-left: 20px;
        padding-right: 20px;
    }
    
    .form-title {
        font-size: 1.3rem;
    }
    
    .error-message {
        margin: 0 10px;
        max-width: calc(100% - 20px);
    }

    .auth-logo {
        margin-bottom: 20px;
    }

    .auth-logo i {
        font-size: 3rem;
        margin-bottom: 10px;
    }

    .auth-logo h2 {
        font-size: 1.8rem;
        font-weight: 800;
    }

    .auth-logo p {
        font-size: 0.95rem;
        font-weight: 600;
    }

    .auth-tips {
        font-size: 0.8rem;
        flex-direction: column;
        gap: 5px;
    }

    .auth-form-wrapper {
        margin: -120px auto 0;
        padding: 0px;
    }
}

/* 横屏模式优化 */
@media (orientation: landscape) and (max-height: 600px) {
    .auth-form-container {
        padding: 10px;
    }
    
    .auth-form {
        padding: 25px 0;
        max-width: 350px;
    }

    .auth-form > * {
        padding-left: 25px;
        padding-right: 25px;
    }
    
    .form-title {
        font-size: 1.3rem;
        margin-bottom: 5px;
    }
    
    .form-subtitle {
        margin-bottom: 20px;
    }
    
    .form-group {
        margin-bottom: 20px;
    }
}

/* 暗色模式支持 */
@media (prefers-color-scheme: dark) {
    .auth-form {
        background: #2c3e50;
        color: #ecf0f1;
    }
    
    .form-title {
        color: #ecf0f1;
    }
    
    .form-subtitle {
        color: #bdc3c7;
    }
    
    .form-group label {
        color: #ecf0f1;
    }
    
    .form-group input {
        background: #34495e;
        border-color: #4a5568;
        color: #ecf0f1;
    }
    
    .form-group input:focus {
        background: #2d3748;
        border-color: #667eea;
    }
    
    .form-group input::placeholder {
        color: #718096;
    }
    
    .password-toggle {
        color: #a0aec0;
    }
    
    .password-toggle:hover {
        color: #667eea;
    }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
    .back-btn:hover {
        transform: none;
    }

    /* 增加触摸目标大小 */
    .back-btn {
        min-height: 44px;
        min-width: 44px;
    }

    .auth-btn:hover {
        transform: none;
        box-shadow: none;
    }
}
    .auth-links {
        border-top-color: #4a5568;
    }
}
