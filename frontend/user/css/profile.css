/* 个人中心页面样式 */

/* 基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

/* 头部导航样式 */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.home-btn, .logout-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 5px;
}

.home-btn:hover, .logout-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.header .title {
    color: white;
    font-size: 1.5rem;
    font-weight: 600;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

/* 主内容区域 */
.main {
    padding: 20px;
    max-width: 600px;
    margin: 0 auto;
}

/* 用户信息区域 */
.user-info {
    background: #2c3e50;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    color: white;
}

.user-line {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 1.1rem;
}

.user-left {
    display: flex;
    align-items: center;
    gap: 8px;
}

.user-right {
    display: flex;
    align-items: center;
    gap: 8px;
}

.user-label {
    color: #bdc3c7;
    font-weight: 500;
}

.nickname, .username {
    color: white;
    font-weight: 600;
}

/* 会员信息区域 */
.membership-section {
    margin-bottom: 20px;
}

.membership-list {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.membership-card {
    background: #2c3e50;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    color: white;
}

.membership-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.25);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.lottery-name {
    font-size: 1.3rem;
    font-weight: 600;
    color: white;
}

.days-info {
    font-size: 0.9rem;
    font-weight: 600;
    padding: 4px 8px;
    border-radius: 4px;
}

.membership-info {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.95rem;
}

.info-label {
    color: #bdc3c7;
    font-weight: 500;
}

.info-value {
    color: white;
    font-weight: 600;
}

.remaining-days {
    color: #27ae60;
    font-weight: 700;
}

.expired-days {
    color: #e74c3c;
    font-weight: 700;
}

/* 密码管理区域 */
.password-section {
    background: white;
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.section-title {
    color: #333;
    font-size: 1.2rem;
    font-weight: 700;
    margin-bottom: 20px;
    text-align: center;
}

.password-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-group label {
    font-weight: 600;
    color: #333;
    font-size: 0.9rem;
}

.input-wrapper {
    position: relative;
}

.form-group input {
    width: 100%;
    padding: 15px 50px 15px 15px;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.form-group input:focus {
    outline: none;
    border-color: #00ff88;
    background: white;
    box-shadow: 0 0 0 3px rgba(0, 255, 136, 0.1);
}

.toggle-password {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    font-size: 1rem;
    transition: color 0.3s ease;
}

.toggle-password:hover {
    color: #00ff88;
}

.form-hint {
    color: #666;
    font-size: 0.8rem;
}

.change-password-btn {
    background: linear-gradient(135deg, #00ff88, #00cc6a);
    color: white;
    border: none;
    padding: 15px;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-top: 10px;
}

.change-password-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 255, 136, 0.4);
}

.change-password-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* 加载状态 */
.loading {
    text-align: center;
    padding: 40px 20px;
    color: white;
}

.loading i {
    font-size: 2rem;
    margin-bottom: 15px;
    display: block;
}

.loading p {
    font-size: 1rem;
    opacity: 0.8;
}

/* 消息提示 */
.message-toast {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 15px 25px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    gap: 10px;
    z-index: 1000;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.message-toast.hidden {
    opacity: 0;
    transform: translateX(-50%) translateY(-20px);
    pointer-events: none;
}

.message-toast.success {
    background: rgba(0, 255, 136, 0.9);
}

.message-toast.error {
    background: rgba(244, 67, 54, 0.9);
}

/* 工具类 */
.hidden {
    display: none !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .header {
        padding: 12px 15px;
    }

    .home-btn, .logout-btn {
        padding: 8px 12px;
        font-size: 0.8rem;
    }

    .home-btn i, .logout-btn i {
        font-size: 0.9rem;
    }

    .home-btn:hover, .logout-btn:hover {
        transform: none;
    }

    /* 增加触摸目标大小 */
    .home-btn, .logout-btn {
        min-height: 44px;
        min-width: 44px;
    }

    .main {
        padding: 15px;
    }

    .user-info {
        padding: 18px;
    }

    .user-line {
        flex-direction: column;
        gap: 8px;
        align-items: flex-start;
    }

    .user-left, .user-right {
        justify-content: flex-start;
    }

    .membership-card {
        padding: 18px;
    }

    .password-section {
        padding: 20px;
    }
}

@media (max-width: 480px) {
    .header {
        padding: 10px 12px;
    }

    .home-btn, .logout-btn {
        padding: 6px 10px;
        font-size: 0.7rem;
    }

    .main {
        padding: 12px;
    }

    .user-info {
        padding: 15px;
    }

    .user-line {
        font-size: 1rem;
    }

    .membership-card {
        padding: 15px;
    }

    .password-section {
        padding: 15px;
    }

    .form-group input {
        padding: 12px 45px 12px 12px;
    }

    .change-password-btn {
        padding: 12px;
    }
}